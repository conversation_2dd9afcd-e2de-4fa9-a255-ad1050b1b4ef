package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;
import com.ruoyi.system.service.ISacSignaturesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * sys_user表签名服务实现类
 * 专门处理sys_user表的user_name+password+phonenumber字段签名
 */
@Slf4j
@Service
public class SysUserSignServiceImpl {
    
    @Autowired
    private ISacSignaturesService sacSignaturesService;
    
    // 常量定义
    private static final String TABLE_NAME = "sys_user";
    private static final String TABLE_FIELD = "user_name,password,phonenumber";
    
    /**
     * 为sys_user记录创建或更新签名
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param password 密码
     * @param phoneNumber 手机号
     * @return 签名操作结果
     */
    public int signUserData(Long userId, String userName, String password, String phoneNumber) {
        try {
            log.info("开始用户数据签名 - 用户ID: {}, 用户名: {}", userId, userName);

            // 构建待签名数据
            String dataToSign = buildSignData(userName, password, phoneNumber);

            // 记录签名原文详细信息
            log.info("用户数据签名原文 - 用户ID: {}, 用户名: {}, 数据长度: {}, 原文内容: [{}]",
                userId, userName,
                dataToSign != null ? dataToSign.length() : 0,
                dataToSign != null ? dataToSign : "null");

            // 构建签名请求
            SacSignaturesReqDto reqDto = new SacSignaturesReqDto();
            reqDto.setTableName(TABLE_NAME);
            reqDto.setTableField(TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(userId));
            reqDto.setDataToSign(dataToSign);

            log.info("用户数据签名请求 - 表名: {}, 字段: {}, 记录ID: {}",
                TABLE_NAME, TABLE_FIELD, userId);

            // 执行签名
            int result = sacSignaturesService.updateSignatures(reqDto);

            if (result > 0) {
                log.info("用户数据签名成功 - 用户ID: {}, 用户名: {}, 操作结果: {}", userId, userName, result);
            } else {
                log.warn("用户数据签名失败 - 用户ID: {}, 用户名: {}, 操作结果: {}", userId, userName, result);
            }

            return result;
        } catch (Exception e) {
            log.error("用户数据签名异常 - 用户ID: {}, 用户名: {}, 错误: {}", userId, userName, e.getMessage(), e);
            throw new RuntimeException("用户数据签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证sys_user记录的签名
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param password 密码
     * @param phoneNumber 手机号
     * @return 验证结果，true表示签名有效或未签名，false表示签名无效
     */
    public boolean verifyUserData(Long userId, String userName, String password, String phoneNumber) {
        try {
            // 构建待验证数据
            String dataToVerify = buildSignData(userName, password, phoneNumber);
            
            // 构建验证请求
            VerifySignatureReqDto reqDto = new VerifySignatureReqDto();
            reqDto.setTableName(TABLE_NAME);
            reqDto.setTableField(TABLE_FIELD);
            reqDto.setRecordId(String.valueOf(userId));
            reqDto.setDataToVerify(dataToVerify);
            
            // 执行验证
            boolean result = sacSignaturesService.verifySignature(reqDto);
            
            if (result) {
                log.debug("用户数据签名验证通过，用户ID：{}，用户名：{}", userId, userName);
            } else {
                log.warn("用户数据签名验证失败，用户ID：{}，用户名：{}", userId, userName);
            }
            
            return result;
        } catch (Exception e) {
            log.error("用户数据签名验证异常，用户ID：{}，用户名：{}，错误：{}", userId, userName, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 构建待签名/验证的数据
     * 直接拼接user_name、password、phonenumber字段值
     *
     * @param userName 用户名
     * @param password 密码
     * @param phoneNumber 手机号
     * @return 待签名的数据字符串
     */
    private String buildSignData(String userName, String password, String phoneNumber) {
        StringBuilder sb = new StringBuilder();

        // 直接拼接字段值
        sb.append(userName != null ? userName : "");
        sb.append(password != null ? password : "");
        sb.append(phoneNumber != null ? phoneNumber : "");

        return sb.toString();
    }
}
