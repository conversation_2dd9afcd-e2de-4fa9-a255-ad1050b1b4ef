package com.ruoyi.system.service.impl;

import com.ruoyi.sac.service.SignService;
import com.ruoyi.system.domain.dto.SacSignaturesReqDto;
import com.ruoyi.system.domain.dto.VerifySignatureReqDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.domain.SacSignatures;
import com.ruoyi.system.mapper.SacSignaturesMapper;
import com.ruoyi.system.service.ISacSignaturesService;

import javax.annotation.Resource;

/**
 * 密评签名服务实现类
 */
@Slf4j
@Service
public class SacSignaturesServiceImpl implements ISacSignaturesService {
    
    @Autowired
    private SacSignaturesMapper sacSignaturesMapper;


    @Resource
    private SignService signService;

    
    /**
     * 更新签名记录
     */
    @Override
    public int updateSignatures(SacSignaturesReqDto reqDto) {
        String tableName = reqDto.getTableName();
        String tableField = reqDto.getTableField();
        String recordId = reqDto.getRecordId();
        String dataToSign = reqDto.getDataToSign();

        log.info("开始更新签名 - 表名: {}, 字段: {}, 记录ID: {}", tableName, tableField, recordId);

        // 记录待签名数据的详细信息
        log.info("待签名数据详情 - 表名: {}, 记录ID: {}, 数据长度: {}, 数据内容: [{}]",
            tableName, recordId,
            dataToSign != null ? dataToSign.length() : 0,
            dataToSign != null ? dataToSign : "null");

        SacSignatures signatures = selectSignaturesByTableAndRecord(tableName, tableField, recordId);
        boolean isNew = false;
        if (signatures == null) {
            log.info("创建新签名记录 - 表名: {}, 记录ID: {}", tableName, recordId);
            signatures = new SacSignatures();
            signatures.setTableName(tableName);
            signatures.setTableField(tableField);
            signatures.setRecordId(recordId);
            isNew = true;
        } else {
            log.info("更新现有签名记录 - 表名: {}, 记录ID: {}, 签名ID: {}",
                tableName, recordId, signatures.getId());
        }

        try {
            // 签名
            com.haitai.response.ResultCode<String> signResult = signService.signData(dataToSign);

            log.info("签名生成结果 - 表名: {}, 记录ID: {}, 响应码: {}, 签名值长度: {}",
                tableName, recordId, signResult.getCode(),
                signResult.getData() != null ? signResult.getData().length() : 0);

            if (signResult.getCode() != 0) {
                log.error("签名生成失败 - 表名: {}, 记录ID: {}, 响应码: {}", tableName, recordId, signResult.getCode());
                throw new RuntimeException("签名失败: " + dataToSign);
            }

            signatures.setSignValue(signResult.getData());
            signatures.setCreateTime(new java.util.Date());

            int result;
            if (isNew) {
                result = sacSignaturesMapper.insert(signatures);
                log.info("签名记录创建结果 - 表名: {}, 记录ID: {}, 操作结果: {}, 新签名ID: {}",
                    tableName, recordId, result, signatures.getId());
            } else {
                result = sacSignaturesMapper.updateByPrimaryKey(signatures);
                log.info("签名记录更新结果 - 表名: {}, 记录ID: {}, 操作结果: {}", tableName, recordId, result);
            }

            return result;
        } catch (Exception e) {
            log.error("签名异常 - 表名: {}, 记录ID: {}, 错误: {}", tableName, recordId, e.getMessage(), e);
            throw new RuntimeException("签名异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据表名和记录ID查询签名记录
     */
    @Override
    public SacSignatures selectSignaturesByTableAndRecord(String tableName,String tableField, String recordId) {
        SacSignatures query = new SacSignatures();
        query.setTableName(tableName);
        query.setRecordId(recordId);
        query.setTableField(tableField);
        return sacSignaturesMapper.selectByTableAndRecord(query);
    }
    
    /**
     * 验证签名
     */
    @Override
    public boolean verifySignature(VerifySignatureReqDto reqDto) {
        String tableName = reqDto.getTableName();
        String tableField = reqDto.getTableField();
        String recordId = reqDto.getRecordId();
        String dataToVerify = reqDto.getDataToVerify();

        log.info("开始验证签名 - 表名: {}, 字段: {}, 记录ID: {}", tableName, tableField, recordId);

        // 记录待验证数据的详细信息
        log.info("待验证数据详情 - 表名: {}, 记录ID: {}, 数据长度: {}, 数据内容: [{}]",
            tableName, recordId,
            dataToVerify != null ? dataToVerify.length() : 0,
            dataToVerify != null ? dataToVerify : "null");

        SacSignatures signatures = selectSignaturesByTableAndRecord(tableName, tableField, recordId);

        if (signatures == null || signatures.getSignValue() == null) {
            log.info("未找到签名记录，忽略验证 - 表名: {}, 记录ID: {}", tableName, recordId);
            // 未签名数据暂时忽略
            return true;
        }

        log.info("获取到签名记录 - 表名: {}, 记录ID: {}, 签名ID: {}, 签名值长度: {}",
            tableName, recordId, signatures.getId(),
            signatures.getSignValue() != null ? signatures.getSignValue().length() : 0);

        try {
            com.haitai.response.ResultCode<Boolean> verifyResult = signService.verifySignedData(dataToVerify, signatures.getSignValue());

            boolean isValid = verifyResult.getCode() == 0 && Boolean.TRUE.equals(verifyResult.getData());

            log.info("签名验证结果 - 表名: {}, 记录ID: {}, 验证结果: {}, 响应码: {}, 响应数据: {}",
                tableName, recordId, isValid, verifyResult.getCode(), verifyResult.getData());

            return isValid;
        } catch (Exception e) {
            log.error("签名验证异常 - 表名: {}, 记录ID: {}, 错误: {}", tableName, recordId, e.getMessage(), e);
            return false;
        }
    }
} 