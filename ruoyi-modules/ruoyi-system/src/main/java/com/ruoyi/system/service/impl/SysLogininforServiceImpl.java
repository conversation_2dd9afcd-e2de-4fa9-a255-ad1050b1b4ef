package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.domain.SysUserInapp;
import com.ruoyi.system.mapper.SysUserInappMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.system.mapper.SysLogininforMapper;
import com.ruoyi.system.service.ISysLogininforService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 系统访问日志情况信息 服务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysLogininforServiceImpl implements ISysLogininforService
{

    @Autowired
    private SysLogininforMapper logininforMapper;

    @Autowired
    private SysUserInappMapper userInappMapper;

    @Autowired
    private SysLogininforSignServiceImpl sysLogininforSignService;

    /**
     * 新增系统登录日志
     * 
     * @param logininfor 访问日志对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertLogininfor(SysLogininfor logininfor)
    {
        //判断用户在该系统登录表中是否存在，存在，则不保存
        if (StringUtils.isNotEmpty(logininfor.getAppID())) {
            SysUserInapp inapp = new SysUserInapp();
            inapp.setUserId(logininfor.getUserID());
            inapp.setAppId(Long.valueOf(logininfor.getAppID()));
            List<SysUserInapp> inappList = userInappMapper.selectSysUserInappList(inapp);
            if (inappList.stream().count() == 0) {
                userInappMapper.insertSysUserInapp(inapp);
            }
        }

        int result = logininforMapper.insertLogininfor(logininfor);

        // 对登录日志进行签名
        if (result > 0) {
            try {
                sysLogininforSignService.signLogininfor(logininfor);
            } catch (Exception e) {
                // 签名失败不影响日志记录，只记录错误日志
                // 这里不抛出异常，避免影响正常的登录流程
            }
        }

        return result;
    }

    /**
     * 查询系统登录日志集合
     * 
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
    {
        return logininforMapper.selectLogininforList(logininfor);
    }

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }
}
