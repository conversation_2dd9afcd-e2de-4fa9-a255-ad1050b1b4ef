<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SacSignaturesMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.SacSignatures">
    <!--@mbg.generated-->
    <!--@Table sac_signatures-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="table_field" jdbcType="VARCHAR" property="tableField" />
    <result column="record_id" jdbcType="VARCHAR" property="recordId" />
    <result column="sign_value" jdbcType="VARCHAR" property="signValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `table_name`, table_field, record_id, sign_value, create_time
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.SacSignatures" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sac_signatures (`table_name`, table_field, record_id,
      sign_value, create_time)
    values (#{tableName,jdbcType=VARCHAR}, #{tableField,jdbcType=VARCHAR}, #{recordId,jdbcType=VARCHAR},
      #{signValue,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.system.domain.SacSignatures">
    <!--@mbg.generated-->
    update sac_signatures
    set `table_name` = #{tableName,jdbcType=VARCHAR},
      table_field = #{tableField,jdbcType=VARCHAR},
      record_id = #{recordId,jdbcType=VARCHAR},
      sign_value = #{signValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByTableAndRecord" resultMap="BaseResultMap">
    select *
    from sac_signatures
    where `table_name` = #{tableName}
      and table_field = #{tableField}
      and record_id = #{recordId}
  </select>
</mapper>