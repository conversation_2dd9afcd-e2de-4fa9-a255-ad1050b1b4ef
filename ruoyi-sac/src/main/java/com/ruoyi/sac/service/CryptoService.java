package com.ruoyi.sac.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.sac.domain.request.EncryptDecryptRequest;
import com.ruoyi.sac.domain.request.GenDataKeyRequest;
import com.ruoyi.sac.domain.response.*;
import com.ruoyi.sac.domain.api.EncryptRequestForApi;
import com.ruoyi.sac.domain.api.DecryptRequestForApi;
import com.ruoyi.sac.domain.api.BatchEncryptRequestForApi;
import com.ruoyi.sac.domain.api.BatchDecryptRequestForApi;
import com.ruoyi.sac.domain.request.BatchEncryptRequest;
import com.ruoyi.sac.domain.request.BatchDecryptRequest;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 商用密码加密服务类
 * 提供符合GB/T 39786-2021标准的密码应用功能：
 * 1. 生成数据密钥
 * 2. 数据加密
 * 3. 数据解密
 * 4. 批量数据加密
 * 5. 批量数据解密
 */
@Service
public class CryptoService {
    private static final Logger log = LoggerFactory.getLogger(CryptoService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 加密服务基础URL，从配置文件中读取
     */
    @Value("${crypto.service.base-url:http://10.16.38.28:18849}")
    private String baseUrl;

    /**
     * 授权头，从配置文件中读取
     */
    @Value("${crypto.service.authorization:Digest algo=SM3 realm=f8b7f5167a71440f9a2092b42de9d694}")
    private String authorization;

    /**
     * 本地缓存的keyId
     */
    private volatile String cachedKeyId;

    /**
     * 生成数据密钥
     * 使用SM4算法生成数据密钥
     *
     * @param request 生成数据密钥请求参数
     * @return 生成数据密钥响应结果
     * @throws Exception 生成数据密钥失败时抛出异常
     */
    public GenDataKeyResponse genDataKey(GenDataKeyRequest request) throws Exception {
        String url = baseUrl + "/eds-service/v4/genDataKey";
        return post(url, request, GenDataKeyResponse.class);
    }

    /**
     * 数据加密
     * 使用数据密钥对数据明文进行加密
     *
     * @param request 加密请求参数
     * @return 加密响应结果
     * @throws Exception 加密失败时抛出异常
     */
    public EncryptResponse encrypt(EncryptDecryptRequest request) throws Exception {
        String url = baseUrl + "/eds-service/v4/encrypt";
        return post(url, request, EncryptResponse.class);
    }

    /**
     * 数据解密
     * 使用数据密钥对数据密文进行解密
     *
     * @param request 解密请求参数
     * @return 解密响应结果
     * @throws Exception 解密失败时抛出异常
     */
    public DecryptResponse decrypt(EncryptDecryptRequest request) throws Exception {
        String url = baseUrl + "/eds-service/v4/decrypt";
        return post(url, request, DecryptResponse.class);
    }

    /**
     * 批量数据加密
     * 使用数据密钥对数据明文进行批量加密
     *
     * @param request 批量加密请求参数
     * @return 批量加密响应结果
     * @throws Exception 批量加密失败时抛出异常
     */
    public BatchEncryptResponse batchEncrypt(BatchEncryptRequest request) throws Exception {
        String url = baseUrl + "/eds-service/v4/batchEncrypt";
        return post(url, request, BatchEncryptResponse.class);
    }

    /**
     * 批量数据解密
     * 使用数据密钥对数据密文进行批量解密
     *
     * @param request 批量解密请求参数
     * @return 批量解密响应结果
     * @throws Exception 批量解密失败时抛出异常
     */
    public BatchDecryptResponse batchDecrypt(BatchDecryptRequest request) throws Exception {
        String url = baseUrl + "/eds-service/v4/batchDecrypt";
        return post(url, request, BatchDecryptResponse.class);
    }

    /**
     * 发送POST请求到加密服务
     *
     * @param url 请求URL
     * @param request 请求参数
     * @param responseType 响应类型
     * @return 响应结果
     * @throws Exception 请求失败时抛出异常
     */
    private <T> T post(String url, Object request, Class<T> responseType) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", authorization);
            String requestJson = objectMapper.writeValueAsString(request);
            httpPost.setEntity(new StringEntity(requestJson, StandardCharsets.UTF_8));
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseJson = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                return objectMapper.readValue(responseJson, responseType);
            }
        } catch (Exception e) {
            log.error("调用加密服务失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取密钥keyId（内部封装）
     */
    private String getKeyId() throws Exception {
        if (cachedKeyId != null) {
            return cachedKeyId;
        }
        GenDataKeyRequest keyRequest = new GenDataKeyRequest();
        keyRequest.setTransId(String.valueOf(System.currentTimeMillis()));
        keyRequest.setEncode("HEX");
        keyRequest.setKeyType("SM4");
        keyRequest.setKeyLength(128);
        GenDataKeyRespDto keyResp = post(baseUrl + "/eds-service/v4/genDataKey", keyRequest, GenDataKeyRespDto.class);
        if (keyResp != null && keyResp.getData() != null && keyResp.getData().getKeyId() != null) {
            cachedKeyId = keyResp.getData().getKeyId();
            return cachedKeyId;
        }
        throw new RuntimeException("获取密钥keyId失败");
    }

    // HEX工具方法
    private static String toHex(String str) {
        if (str == null) return null;
        StringBuilder sb = new StringBuilder();
        for (char c : str.toCharArray()) {
            sb.append(String.format("%02x", (int) c));
        }
        return sb.toString();
    }
    private static String hexToString(String hex) {
        if (hex == null) return null;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < hex.length(); i += 2) {
            sb.append((char) Integer.parseInt(hex.substring(i, i + 2), 16));
        }
        return sb.toString();
    }

    // 对外API：自动获取密钥并加密（单个）
    public EncryptResponse encryptForApi(EncryptRequestForApi apiRequest) throws Exception {
        String keyId = getKeyId();
        EncryptDecryptRequest encryptRequest = new EncryptDecryptRequest();
        encryptRequest.setTransId(String.valueOf(System.currentTimeMillis()));
        encryptRequest.setEncode("HEX");
        encryptRequest.setKeyId(keyId);
        encryptRequest.setSymEncAlg("SM4");
        encryptRequest.setMode("CBC");
        encryptRequest.setIv("31323334353637383132333435363738");
        encryptRequest.setPadding("PKCS7Padding");
        encryptRequest.setData(apiRequest.getData());
        EncryptResponse resp = this.encrypt(encryptRequest);
        if (resp != null && resp.getMsg() != null && resp.getMsg().contains("APP数据密钥不存在或已过期")) {
            cachedKeyId = null;
            keyId = getKeyId();
            encryptRequest.setKeyId(keyId);
            resp = this.encrypt(encryptRequest);
        }
        // 单个加密返回不需要转明文
        return resp;
    }

    // 对外API：自动获取密钥并解密（单个）
    public DecryptResponse decryptForApi(DecryptRequestForApi apiRequest) throws Exception {
        String keyId = getKeyId();
        EncryptDecryptRequest decryptRequest = new EncryptDecryptRequest();
        decryptRequest.setTransId(String.valueOf(System.currentTimeMillis()));
        decryptRequest.setEncode("HEX");
        decryptRequest.setKeyId(keyId);
        decryptRequest.setSymEncAlg("SM4");
        decryptRequest.setMode("CBC");
        decryptRequest.setIv("31323334353637383132333435363738");
        decryptRequest.setPadding("PKCS7Padding");
        decryptRequest.setData(apiRequest.getData());
        DecryptResponse resp = this.decrypt(decryptRequest);
        if (resp != null && resp.getMsg() != null && resp.getMsg().contains("APP数据密钥不存在或已过期")) {
            cachedKeyId = null;
            keyId = getKeyId();
            decryptRequest.setKeyId(keyId);
            resp = this.decrypt(decryptRequest);
        }
        return resp;
    }

    // 对外API：自动获取密钥并批量加密
    public BatchEncryptResponse batchEncryptForApi(BatchEncryptRequestForApi apiRequest) throws Exception {
        String keyId = getKeyId();
        BatchEncryptRequest batchRequest = new BatchEncryptRequest();
        batchRequest.setTransId(String.valueOf(System.currentTimeMillis()));
        batchRequest.setEncode("HEX");
        batchRequest.setSymEncAlg("SM4");
        batchRequest.setMode("CBC");
        batchRequest.setIv("31323334353637383132333435363738");
        batchRequest.setPadding("PKCS7Padding");
        java.util.List<BatchEncryptRequest.BatchData> batchDataList = new java.util.ArrayList<>();
        for (java.util.Map<String, String> map : apiRequest.getDataList()) {
            BatchEncryptRequest.BatchData bd = new BatchEncryptRequest.BatchData();
            bd.setTag(map.getOrDefault("tag", null));
            bd.setKeyId(keyId);
            // 使用setExtraField方法设置需要加密的字段，排除tag和keyId字段
            for (java.util.Map.Entry<String, String> entry : map.entrySet()) {
                if (!"tag".equals(entry.getKey()) && !"keyId".equals(entry.getKey())) {
                    bd.setExtraField(entry.getKey(), entry.getValue());
                }
            }
            batchDataList.add(bd);
        }
        batchRequest.setBatchData(batchDataList);
        BatchEncryptResponse resp = this.batchEncrypt(batchRequest);
        if (resp != null && resp.getMsg() != null && resp.getMsg().contains("APP数据密钥不存在或已过期")) {
            cachedKeyId = null;
            keyId = getKeyId();
            for (BatchEncryptRequest.BatchData bd : batchDataList) {
                bd.setKeyId(keyId);
            }
            resp = this.batchEncrypt(batchRequest);
        }
        // 批量加密返回不需要转明文
        return resp;
    }

    // 对外API：自动获取密钥并批量解密
    public BatchDecryptResponse batchDecryptForApi(BatchDecryptRequestForApi apiRequest) throws Exception {
        String keyId = getKeyId();
        BatchDecryptRequest batchRequest = new BatchDecryptRequest();
        batchRequest.setTransId(String.valueOf(System.currentTimeMillis()));
        batchRequest.setEncode("HEX");
        batchRequest.setSymEncAlg("SM4");
        batchRequest.setMode("CBC");
        batchRequest.setIv("31323334353637383132333435363738");
        batchRequest.setPadding("PKCS7Padding");
        java.util.List<BatchDecryptRequest.BatchData> batchDataList = new java.util.ArrayList<>();
        for (java.util.Map<String, String> map : apiRequest.getDataList()) {
            BatchDecryptRequest.BatchData bd = new BatchDecryptRequest.BatchData();
            bd.setTag(map.getOrDefault("tag", null));
            bd.setKeyId(keyId);
            // 使用setExtraField方法设置需要解密的字段，排除tag和keyId字段
            for (java.util.Map.Entry<String, String> entry : map.entrySet()) {
                if (!"tag".equals(entry.getKey()) && !"keyId".equals(entry.getKey())) {
                    bd.setExtraField(entry.getKey(), entry.getValue());
                }
            }
            batchDataList.add(bd);
        }
        batchRequest.setBatchData(batchDataList);
        BatchDecryptResponse resp = this.batchDecrypt(batchRequest);
        if (resp != null && resp.getMsg() != null && resp.getMsg().contains("APP数据密钥不存在或已过期")) {
            cachedKeyId = null;
            keyId = getKeyId();
            for (BatchDecryptRequest.BatchData bd : batchDataList) {
                bd.setKeyId(keyId);
            }
            resp = this.batchDecrypt(batchRequest);
        }

        return resp;
    }
} 