package com.ruoyi.sac.domain.request;

import lombok.Data;

/**
 * 加密解密请求实体
 */
@Data
public class EncryptDecryptRequest {
    /**
     * 业务ID，使用当前调用时间，格式为时间戳（建议业务系统进行日志记录，方便根据transId排查问题）
     */
    private String transId;

    /**
     * 编码类型:十六进制编码(HEX),BASE64编码(BASE64)，无编码(NON),默认NON
     */
    private String encode;

    /**
     * 数据密钥唯一标识，参数为空时由平台指定
     */
    private String keyId;

    /**
     * 对称加密算法:SM4，默认使用SM4
     */
    private String symEncAlg;

    /**
     * 加密模式:ECB/CBC，默认使用CBC
     */
    private String mode;

    /**
     * IV值，mode为CBC时有效。
     * 注意：如果mode为CBC，iv值填写16字节的数据，编码方式由encode指定，参数为空时由平台指定
     */
    private String iv;

    /**
     * 填充方式:NoPadding、PKCS7Padding，默认使用PKCS7Padding
     */
    private String padding;

    /**
     * 数据明文，编码方式由encode指定
     */
    private String data;
} 