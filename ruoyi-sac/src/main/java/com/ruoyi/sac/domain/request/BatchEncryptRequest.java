package com.ruoyi.sac.domain.request;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BatchEncryptRequest {
    private String transId;
    private String encode;
    private String symEncAlg;
    private String mode;
    private String iv;
    private String padding;
    private List<BatchData> batchData;

    @Data
    public static class BatchData {
        private String tag;
        private String keyId;
        private Map<String, String> extraFields = new HashMap<>();

        @JsonAnySetter
        public void setExtraField(String key, String value) {
            if (!"tag".equals(key) && !"keyId".equals(key)) {
                extraFields.put(key, value);
            }
        }

        @JsonAnyGetter
        public Map<String, String> getExtraFields() {
            return extraFields;
        }
    }
} 